name: Build and Test

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-and-test:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    
    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-
    
    - name: Restore dependencies
      run: dotnet restore DTXMania.sln

    - name: Build solution
      run: dotnet build DTXMania.sln --configuration Debug --no-restore

    - name: Run tests
      run: |
        dotnet test DTXMania.Test/DTXMania.Test.csproj \
          --configuration Debug \
          --no-build \
          --verbosity normal \
          --collect:"XPlat Code Coverage" \
          --results-directory ./TestResults \
          --logger trx

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-windows
        path: ./TestResults

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v4
      with:
        directory: ./TestResults
        fail_ci_if_error: false
        verbose: true

  build-artifacts:
    runs-on: windows-latest
    if: github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    
    - name: Restore dependencies
      run: dotnet restore DTXMania.sln
    
    - name: Build Windows Release
      run: dotnet build DTXMania.Windows/DTXMania.Windows.csproj --configuration Release --no-restore
    
    - name: Publish Windows
      run: |
        dotnet publish DTXMania.Windows/DTXMania.Windows.csproj \
          --configuration Release \
          --output ./publish/windows \
          --no-restore \
          --self-contained false
    
    - name: Upload Windows artifacts
      uses: actions/upload-artifact@v4
      with:
        name: dtxmania-windows-${{ github.sha }}
        path: ./publish/windows
