<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>DTXMania.Test</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="MonoGame.Framework.DesktopGL" Version="3.8.*">
      <PrivateAssets>All</PrivateAssets>
    </PackageReference>
    <PackageReference Include="NVorbis" Version="0.10.5" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="9.0.5" />
  </ItemGroup>
</Project>